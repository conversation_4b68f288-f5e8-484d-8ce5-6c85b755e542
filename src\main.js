import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import router from './router'
import App from './App.vue'
import i18n from './locales' // 导入i18n

// 导入Vant
import {
  Popup,
  Toast, 
  Dialog, 
  Notify, 
  ImagePreview, 
  Button, 
  Form, 
  Field, 
  Cell, 
  CellGroup, 
  Icon, 
  Checkbox,
  Tabbar,
  TabbarItem,
  DropdownMenu, 
  DropdownItem,
  Search,
  Tabs,
  Tab,
  TreeSelect,
  List,
  Empty,
  SwipeCell,
  NavBar,
  Image as VanImage,
  Grid,
  GridItem,
  Swipe,
  SwipeItem,
  Card,
  Tag,
  Uploader,
  DatePicker,
  Picker,
  Cascader,
  RadioGroup,
  Radio,
  Loading,
  PullRefresh,
  Skeleton,
  Pagination,
  ActionSheet,
  Steps,
  Step,
  Sticky,
  Calendar,
  Stepper,
  Popover,
  Lazyload 

} from 'vant'
import 'vant/lib/index.css'
// 导入自定义Vant主题
import './assets/styles/vant-theme.scss'

// 导入配置
import { BASE_URL, BASE_IMG_URL, BASE_FILE_URL } from '@/utils/config'
// 导入多语言配置系统
import globalConfig from '@/config/index.ts'

// 全局样式
import './assets/styles/index.scss'
// 强制横竖屏样式
import './styles/force-orientation.css'

// 导入方向状态存储
import { useOrientationStore } from '@/stores/orientation'

// 导出全局变量，供JS文件使用
export const $base_host = BASE_URL
export const $base_img_url = BASE_IMG_URL
export const $base_downFileByPath_url = BASE_FILE_URL

// 创建Pinia状态管理实例
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

// 创建Vue应用实例
const app = createApp(App)

// 注册Vant组件
app.use(Toast)
app.use(Dialog)
app.use(Notify)
app.use(ImagePreview)
app.use(Button)
app.use(Form)
app.use(Field)
app.use(Cell)
app.use(CellGroup)
app.use(Icon)
app.use(Checkbox)
app.use(Tabbar)
app.use(TabbarItem)
app.use(DropdownMenu)
app.use(DropdownItem)
app.use(Search)
app.use(Tabs)
app.use(Tab)
app.use(TreeSelect)
app.use(List)
app.use(Empty)
app.use(SwipeCell)
app.use(NavBar)
app.use(VanImage)
app.use(Popup)
app.use(Grid)
app.use(GridItem)
app.use(Swipe)
app.use(SwipeItem)
app.use(Card)
app.use(Tag)
app.use(Uploader)
app.use(DatePicker)
app.use(Picker)
app.use(Cascader)
app.use(RadioGroup)
app.use(Radio)
app.use(Loading)
app.use(PullRefresh)
app.use(Skeleton)
app.use(Pagination)
app.use(ActionSheet)
app.use(Steps)
app.use(Step)
app.use(Sticky)
app.use(Calendar)
app.use(Stepper)
app.use(Popover)
app.use(Lazyload)

// 注册全局变量
app.provide('$base_host', BASE_URL)
app.provide('$base_img_url', BASE_IMG_URL)
app.provide('$base_downFileByPath_url', BASE_FILE_URL)
// 注册全局配置
app.provide('$globalConfig', globalConfig)

// 注册Pinia和路由
app.use(pinia)
app.use(router)
app.use(i18n) // 注册i18n

// 设置HTML的lang属性
document.querySelector('html').setAttribute('lang', i18n.global.locale.value)

// 挂载应用
app.mount('#app')

// 初始化方向状态
const orientationStore = useOrientationStore()
orientationStore.initOrientation() 